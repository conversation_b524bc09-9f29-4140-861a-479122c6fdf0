import React, { useState, useContext } from 'react';
import { FoodContext } from '../App';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  ChefHat,
  Clock,
  Users,
  Star,
  BookOpen
} from 'lucide-react';

function Recipes() {
  const { recipes, setRecipes } = useContext(FoodContext);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingRecipe, setEditingRecipe] = useState(null);
  const [selectedRecipe, setSelectedRecipe] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    cookTime: '',
    prepTime: '',
    servings: '',
    difficulty: '',
    category: '',
    ingredients: [''],
    instructions: [''],
    tags: '',
    notes: ''
  });

  const categories = [
    'Breakfast', 'Lunch', 'Dinner', 'Snack', 'Dessert', 
    'Appetizer', 'Soup', 'Salad', 'Main Course', 'Side Dish'
  ];

  const difficulties = ['Easy', 'Medium', 'Hard'];

  // Filter recipes based on search term
  const filteredRecipes = recipes.filter(recipe =>
    recipe.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recipe.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    recipe.tags.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const recipeData = {
      ...formData,
      ingredients: formData.ingredients.filter(ing => ing.trim() !== ''),
      instructions: formData.instructions.filter(inst => inst.trim() !== ''),
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
    };

    if (editingRecipe) {
      // Update existing recipe
      setRecipes(recipes.map(recipe => 
        recipe.id === editingRecipe.id 
          ? { ...recipeData, id: editingRecipe.id }
          : recipe
      ));
      setEditingRecipe(null);
    } else {
      // Add new recipe
      const newRecipe = {
        ...recipeData,
        id: Date.now().toString(),
        dateAdded: new Date().toISOString(),
        rating: 0
      };
      setRecipes([...recipes, newRecipe]);
    }
    
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '', description: '', cookTime: '', prepTime: '', servings: '',
      difficulty: '', category: '', ingredients: [''], instructions: [''],
      tags: '', notes: ''
    });
    setShowAddForm(false);
  };

  const handleEdit = (recipe) => {
    setFormData({
      ...recipe,
      tags: recipe.tags ? recipe.tags.join(', ') : ''
    });
    setEditingRecipe(recipe);
    setShowAddForm(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this recipe?')) {
      setRecipes(recipes.filter(recipe => recipe.id !== id));
    }
  };

  const addIngredient = () => {
    setFormData({
      ...formData,
      ingredients: [...formData.ingredients, '']
    });
  };

  const removeIngredient = (index) => {
    setFormData({
      ...formData,
      ingredients: formData.ingredients.filter((_, i) => i !== index)
    });
  };

  const updateIngredient = (index, value) => {
    const newIngredients = [...formData.ingredients];
    newIngredients[index] = value;
    setFormData({
      ...formData,
      ingredients: newIngredients
    });
  };

  const addInstruction = () => {
    setFormData({
      ...formData,
      instructions: [...formData.instructions, '']
    });
  };

  const removeInstruction = (index) => {
    setFormData({
      ...formData,
      instructions: formData.instructions.filter((_, i) => i !== index)
    });
  };

  const updateInstruction = (index, value) => {
    const newInstructions = [...formData.instructions];
    newInstructions[index] = value;
    setFormData({
      ...formData,
      instructions: newInstructions
    });
  };

  return (
    <div className="container">
      <div className="recipes">
        <header className="page-header">
          <div className="flex-between">
            <div>
              <h1 className="text-2xl font-bold mb-4">Recipe Collection</h1>
              <p className="opacity-70">Store and organize your favorite recipes</p>
            </div>
            <button 
              className="btn"
              onClick={() => setShowAddForm(true)}
            >
              <Plus size={20} />
              Add Recipe
            </button>
          </div>
        </header>

        {/* Search Bar */}
        <div className="search-bar mb-4">
          <div className="search-input-container">
            <Search size={20} className="search-icon" />
            <input
              type="text"
              placeholder="Search recipes..."
              className="input search-input"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Recipe Detail Modal */}
        {selectedRecipe && (
          <div className="modal-overlay">
            <div className="modal recipe-detail">
              <div className="flex-between mb-4">
                <h2 className="text-2xl font-bold">{selectedRecipe.name}</h2>
                <button 
                  className="btn btn-secondary"
                  onClick={() => setSelectedRecipe(null)}
                >
                  Close
                </button>
              </div>
              
              <div className="recipe-meta mb-4">
                <div className="flex gap-4 text-sm opacity-70">
                  <span><Clock size={16} className="inline mr-1" />{selectedRecipe.cookTime} min</span>
                  <span><Users size={16} className="inline mr-1" />{selectedRecipe.servings} servings</span>
                  <span>📊 {selectedRecipe.difficulty}</span>
                  <span>🏷️ {selectedRecipe.category}</span>
                </div>
              </div>

              {selectedRecipe.description && (
                <p className="mb-4 opacity-80">{selectedRecipe.description}</p>
              )}

              <div className="grid grid-2">
                <div>
                  <h3 className="text-lg font-bold mb-3">Ingredients</h3>
                  <ul className="ingredient-list">
                    {selectedRecipe.ingredients?.map((ingredient, index) => (
                      <li key={index} className="mb-2">• {ingredient}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-bold mb-3">Instructions</h3>
                  <ol className="instruction-list">
                    {selectedRecipe.instructions?.map((instruction, index) => (
                      <li key={index} className="mb-3">
                        <span className="step-number">{index + 1}</span>
                        {instruction}
                      </li>
                    ))}
                  </ol>
                </div>
              </div>

              {selectedRecipe.notes && (
                <div className="mt-4">
                  <h3 className="text-lg font-bold mb-2">Notes</h3>
                  <p className="opacity-80">{selectedRecipe.notes}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="modal-overlay">
            <div className="modal">
              <h2 className="text-xl font-bold mb-4">
                {editingRecipe ? 'Edit Recipe' : 'Add New Recipe'}
              </h2>
              <form onSubmit={handleSubmit}>
                <input
                  type="text"
                  placeholder="Recipe name"
                  className="input"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />

                <textarea
                  placeholder="Description (optional)"
                  className="input"
                  rows="3"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                />

                <div className="grid grid-2">
                  <input
                    type="number"
                    placeholder="Prep time (minutes)"
                    className="input"
                    value={formData.prepTime}
                    onChange={(e) => setFormData({...formData, prepTime: e.target.value})}
                  />
                  <input
                    type="number"
                    placeholder="Cook time (minutes)"
                    className="input"
                    value={formData.cookTime}
                    onChange={(e) => setFormData({...formData, cookTime: e.target.value})}
                    required
                  />
                </div>

                <div className="grid grid-3">
                  <input
                    type="number"
                    placeholder="Servings"
                    className="input"
                    value={formData.servings}
                    onChange={(e) => setFormData({...formData, servings: e.target.value})}
                    required
                  />
                  <select
                    className="input"
                    value={formData.difficulty}
                    onChange={(e) => setFormData({...formData, difficulty: e.target.value})}
                    required
                  >
                    <option value="">Difficulty</option>
                    {difficulties.map(diff => (
                      <option key={diff} value={diff}>{diff}</option>
                    ))}
                  </select>
                  <select
                    className="input"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    required
                  >
                    <option value="">Category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                </div>

                {/* Ingredients */}
                <div className="form-section">
                  <div className="flex-between mb-2">
                    <h3 className="font-bold">Ingredients</h3>
                    <button type="button" className="btn btn-secondary" onClick={addIngredient}>
                      <Plus size={16} />
                    </button>
                  </div>
                  {formData.ingredients.map((ingredient, index) => (
                    <div key={index} className="flex gap-2 mb-2">
                      <input
                        type="text"
                        placeholder="e.g., 2 cups flour"
                        className="input"
                        value={ingredient}
                        onChange={(e) => updateIngredient(index, e.target.value)}
                      />
                      {formData.ingredients.length > 1 && (
                        <button 
                          type="button" 
                          className="btn-icon btn-danger"
                          onClick={() => removeIngredient(index)}
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                {/* Instructions */}
                <div className="form-section">
                  <div className="flex-between mb-2">
                    <h3 className="font-bold">Instructions</h3>
                    <button type="button" className="btn btn-secondary" onClick={addInstruction}>
                      <Plus size={16} />
                    </button>
                  </div>
                  {formData.instructions.map((instruction, index) => (
                    <div key={index} className="flex gap-2 mb-2">
                      <textarea
                        placeholder={`Step ${index + 1}`}
                        className="input"
                        rows="2"
                        value={instruction}
                        onChange={(e) => updateInstruction(index, e.target.value)}
                      />
                      {formData.instructions.length > 1 && (
                        <button 
                          type="button" 
                          className="btn-icon btn-danger"
                          onClick={() => removeInstruction(index)}
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                <input
                  type="text"
                  placeholder="Tags (comma separated)"
                  className="input"
                  value={formData.tags}
                  onChange={(e) => setFormData({...formData, tags: e.target.value})}
                />

                <textarea
                  placeholder="Notes (optional)"
                  className="input"
                  rows="3"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />

                <div className="flex gap-2">
                  <button type="submit" className="btn">
                    {editingRecipe ? 'Update Recipe' : 'Add Recipe'}
                  </button>
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => {
                      resetForm();
                      setEditingRecipe(null);
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Recipe Grid */}
        <div className="grid grid-3">
          {filteredRecipes.map(recipe => (
            <div key={recipe.id} className="card recipe-card">
              <div className="flex-between mb-2">
                <h3 className="font-bold">{recipe.name}</h3>
                <div className="flex gap-1">
                  <button 
                    className="btn-icon"
                    onClick={() => setSelectedRecipe(recipe)}
                  >
                    <BookOpen size={16} />
                  </button>
                  <button 
                    className="btn-icon"
                    onClick={() => handleEdit(recipe)}
                  >
                    <Edit size={16} />
                  </button>
                  <button 
                    className="btn-icon btn-danger"
                    onClick={() => handleDelete(recipe.id)}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
              
              {recipe.description && (
                <p className="text-sm opacity-70 mb-3">{recipe.description}</p>
              )}
              
              <div className="recipe-meta">
                <div className="flex gap-3 text-sm opacity-70 mb-2">
                  <span><Clock size={14} className="inline mr-1" />{recipe.cookTime}min</span>
                  <span><Users size={14} className="inline mr-1" />{recipe.servings}</span>
                  <span>📊 {recipe.difficulty}</span>
                </div>
                <div className="flex-between">
                  <span className="category-tag">{recipe.category}</span>
                  {recipe.tags && recipe.tags.length > 0 && (
                    <div className="tags">
                      {recipe.tags.slice(0, 2).map((tag, index) => (
                        <span key={index} className="tag">#{tag}</span>
                      ))}
                      {recipe.tags.length > 2 && (
                        <span className="tag">+{recipe.tags.length - 2}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredRecipes.length === 0 && (
          <div className="empty-state">
            <ChefHat size={48} className="opacity-50 mb-4" />
            <h3 className="text-lg font-bold mb-2">No recipes found</h3>
            <p className="opacity-70 mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Start building your recipe collection'}
            </p>
            {!searchTerm && (
              <button className="btn" onClick={() => setShowAddForm(true)}>
                <Plus size={20} />
                Add Your First Recipe
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default Recipes;
