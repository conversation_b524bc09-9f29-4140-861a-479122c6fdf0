import React, { useState, useContext } from 'react';
import { FoodContext } from '../App';
import { 
  Plus, 
  Search, 
  Trash2, 
  ShoppingCart,
  Check,
  X,
  Filter
} from 'lucide-react';
import { format } from 'date-fns';

function ShoppingList() {
  const { shoppingList, setShoppingList } = useContext(FoodContext);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [filterCompleted, setFilterCompleted] = useState('all'); // 'all', 'pending', 'completed'
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    quantity: '',
    notes: ''
  });

  const categories = [
    'Produce', 'Meat & Seafood', 'Dairy & Eggs', 'Bakery', 'Pantry', 
    'Frozen', 'Beverages', 'Snacks', 'Health & Beauty', 'Other'
  ];

  // Filter shopping list
  const filteredList = shoppingList.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.category && item.category.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = filterCompleted === 'all' || 
                         (filterCompleted === 'completed' && item.completed) ||
                         (filterCompleted === 'pending' && !item.completed);
    
    return matchesSearch && matchesFilter;
  });

  // Group items by category
  const groupedItems = filteredList.reduce((groups, item) => {
    const category = item.category || 'Other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(item);
    return groups;
  }, {});

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const newItem = {
      ...formData,
      id: Date.now().toString(),
      completed: false,
      dateAdded: new Date().toISOString()
    };
    
    setShoppingList([...shoppingList, newItem]);
    
    // Reset form
    setFormData({
      name: '',
      category: '',
      quantity: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const toggleItemCompleted = (id) => {
    setShoppingList(shoppingList.map(item => 
      item.id === id 
        ? { ...item, completed: !item.completed }
        : item
    ));
  };

  const deleteItem = (id) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      setShoppingList(shoppingList.filter(item => item.id !== id));
    }
  };

  const clearCompleted = () => {
    if (window.confirm('Are you sure you want to remove all completed items?')) {
      setShoppingList(shoppingList.filter(item => !item.completed));
    }
  };

  const markAllCompleted = () => {
    setShoppingList(shoppingList.map(item => ({ ...item, completed: true })));
  };

  const pendingCount = shoppingList.filter(item => !item.completed).length;
  const completedCount = shoppingList.filter(item => item.completed).length;

  return (
    <div className="container">
      <div className="shopping-list">
        <header className="page-header">
          <div className="flex-between">
            <div>
              <h1 className="text-2xl font-bold mb-4">Shopping List</h1>
              <p className="opacity-70">
                {pendingCount} items to buy • {completedCount} completed
              </p>
            </div>
            <div className="flex gap-2">
              {completedCount > 0 && (
                <button 
                  className="btn btn-secondary"
                  onClick={clearCompleted}
                >
                  Clear Completed
                </button>
              )}
              <button 
                className="btn"
                onClick={() => setShowAddForm(true)}
              >
                <Plus size={20} />
                Add Item
              </button>
            </div>
          </div>
        </header>

        {/* Search and Filter Bar */}
        <div className="controls mb-4">
          <div className="search-bar">
            <div className="search-input-container">
              <Search size={20} className="search-icon" />
              <input
                type="text"
                placeholder="Search shopping list..."
                className="input search-input"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="filter-controls">
            <div className="flex gap-2">
              <button 
                className={`filter-btn ${filterCompleted === 'all' ? 'active' : ''}`}
                onClick={() => setFilterCompleted('all')}
              >
                All ({shoppingList.length})
              </button>
              <button 
                className={`filter-btn ${filterCompleted === 'pending' ? 'active' : ''}`}
                onClick={() => setFilterCompleted('pending')}
              >
                Pending ({pendingCount})
              </button>
              <button 
                className={`filter-btn ${filterCompleted === 'completed' ? 'active' : ''}`}
                onClick={() => setFilterCompleted('completed')}
              >
                Completed ({completedCount})
              </button>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        {pendingCount > 0 && (
          <div className="quick-actions mb-4">
            <button 
              className="btn btn-secondary"
              onClick={markAllCompleted}
            >
              <Check size={16} />
              Mark All Complete
            </button>
          </div>
        )}

        {/* Add Item Form */}
        {showAddForm && (
          <div className="modal-overlay">
            <div className="modal">
              <h2 className="text-xl font-bold mb-4">Add Shopping Item</h2>
              <form onSubmit={handleSubmit}>
                <input
                  type="text"
                  placeholder="Item name"
                  className="input"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
                
                <div className="grid grid-2">
                  <select
                    className="input"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                  >
                    <option value="">Select category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                  
                  <input
                    type="text"
                    placeholder="Quantity (optional)"
                    className="input"
                    value={formData.quantity}
                    onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                  />
                </div>

                <textarea
                  placeholder="Notes (optional)"
                  className="input"
                  rows="3"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />

                <div className="flex gap-2">
                  <button type="submit" className="btn">
                    Add Item
                  </button>
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowAddForm(false);
                      setFormData({ name: '', category: '', quantity: '', notes: '' });
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Shopping List Items */}
        <div className="shopping-items">
          {Object.keys(groupedItems).length === 0 ? (
            <div className="empty-state">
              <ShoppingCart size={48} className="opacity-50 mb-4" />
              <h3 className="text-lg font-bold mb-2">No items found</h3>
              <p className="opacity-70 mb-4">
                {searchTerm ? 'Try adjusting your search terms' : 'Start by adding items to your shopping list'}
              </p>
              {!searchTerm && (
                <button className="btn" onClick={() => setShowAddForm(true)}>
                  <Plus size={20} />
                  Add Your First Item
                </button>
              )}
            </div>
          ) : (
            Object.entries(groupedItems).map(([category, items]) => (
              <div key={category} className="category-section">
                <h2 className="category-header">{category} ({items.length})</h2>
                <div className="items-grid">
                  {items.map(item => (
                    <div 
                      key={item.id} 
                      className={`shopping-item ${item.completed ? 'completed' : ''}`}
                    >
                      <div className="item-content">
                        <div className="item-main">
                          <button 
                            className={`checkbox ${item.completed ? 'checked' : ''}`}
                            onClick={() => toggleItemCompleted(item.id)}
                          >
                            {item.completed && <Check size={16} />}
                          </button>
                          
                          <div className="item-details">
                            <h3 className={`item-name ${item.completed ? 'completed-text' : ''}`}>
                              {item.name}
                              {item.quantity && <span className="quantity"> ({item.quantity})</span>}
                            </h3>
                            {item.notes && (
                              <p className="item-notes">{item.notes}</p>
                            )}
                            <p className="item-meta">
                              Added {format(new Date(item.dateAdded), 'MMM dd, yyyy')}
                            </p>
                          </div>
                        </div>
                        
                        <button 
                          className="btn-icon btn-danger"
                          onClick={() => deleteItem(item.id)}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default ShoppingList;
