import React, { useContext } from 'react';
import { FoodContext } from '../App';
import { 
  Package, 
  ChefHat, 
  Calendar, 
  ShoppingCart, 
  AlertTriangle,
  TrendingUp,
  Clock
} from 'lucide-react';
import { format, isAfter, addDays, parseISO } from 'date-fns';

function Dashboard() {
  const { inventory, recipes, mealPlan, shoppingList } = useContext(FoodContext);

  // Calculate dashboard statistics
  const totalItems = inventory.length;
  const totalRecipes = recipes.length;
  const upcomingMeals = Object.values(mealPlan).flat().length;
  const shoppingItems = shoppingList.filter(item => !item.completed).length;

  // Find items expiring soon (within 3 days)
  const expiringSoon = inventory.filter(item => {
    if (!item.expirationDate) return false;
    const expirationDate = parseISO(item.expirationDate);
    const threeDaysFromNow = addDays(new Date(), 3);
    return isAfter(threeDaysFromNow, expirationDate) && isAfter(expirationDate, new Date());
  });

  // Find expired items
  const expiredItems = inventory.filter(item => {
    if (!item.expirationDate) return false;
    const expirationDate = parseISO(item.expirationDate);
    return isAfter(new Date(), expirationDate);
  });

  // Recent recipes (last 5 added)
  const recentRecipes = recipes.slice(-5).reverse();

  const stats = [
    {
      title: 'Total Items',
      value: totalItems,
      icon: Package,
      color: '#667eea'
    },
    {
      title: 'Recipes',
      value: totalRecipes,
      icon: ChefHat,
      color: '#764ba2'
    },
    {
      title: 'Planned Meals',
      value: upcomingMeals,
      icon: Calendar,
      color: '#f093fb'
    },
    {
      title: 'Shopping Items',
      value: shoppingItems,
      icon: ShoppingCart,
      color: '#f5576c'
    }
  ];

  return (
    <div className="container">
      <div className="dashboard">
        <header className="dashboard-header">
          <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
          <p className="opacity-70">Welcome to your Food Management System</p>
        </header>

        {/* Statistics Cards */}
        <div className="grid grid-2 mb-4">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="card stat-card">
                <div className="flex-between">
                  <div>
                    <p className="text-sm opacity-70">{stat.title}</p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className="stat-icon" style={{ backgroundColor: stat.color }}>
                    <Icon size={24} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid grid-2">
          {/* Expiration Alerts */}
          <div className="card">
            <h2 className="text-xl font-bold mb-4 flex">
              <AlertTriangle className="mr-2" size={24} />
              Expiration Alerts
            </h2>
            
            {expiredItems.length > 0 && (
              <div className="alert alert-danger mb-4">
                <h3 className="font-bold text-sm">Expired Items ({expiredItems.length})</h3>
                <ul className="mt-2">
                  {expiredItems.slice(0, 3).map((item, index) => (
                    <li key={index} className="text-sm opacity-70">
                      {item.name} - Expired {format(parseISO(item.expirationDate), 'MMM dd')}
                    </li>
                  ))}
                  {expiredItems.length > 3 && (
                    <li className="text-sm opacity-70">
                      +{expiredItems.length - 3} more items
                    </li>
                  )}
                </ul>
              </div>
            )}

            {expiringSoon.length > 0 && (
              <div className="alert alert-warning">
                <h3 className="font-bold text-sm">Expiring Soon ({expiringSoon.length})</h3>
                <ul className="mt-2">
                  {expiringSoon.slice(0, 3).map((item, index) => (
                    <li key={index} className="text-sm opacity-70">
                      {item.name} - Expires {format(parseISO(item.expirationDate), 'MMM dd')}
                    </li>
                  ))}
                  {expiringSoon.length > 3 && (
                    <li className="text-sm opacity-70">
                      +{expiringSoon.length - 3} more items
                    </li>
                  )}
                </ul>
              </div>
            )}

            {expiredItems.length === 0 && expiringSoon.length === 0 && (
              <p className="text-sm opacity-70">No expiration alerts! 🎉</p>
            )}
          </div>

          {/* Recent Recipes */}
          <div className="card">
            <h2 className="text-xl font-bold mb-4 flex">
              <ChefHat className="mr-2" size={24} />
              Recent Recipes
            </h2>
            
            {recentRecipes.length > 0 ? (
              <div className="recipe-list">
                {recentRecipes.map((recipe, index) => (
                  <div key={index} className="recipe-item">
                    <h3 className="font-bold text-sm">{recipe.name}</h3>
                    <p className="text-sm opacity-70">
                      {recipe.cookTime} min • {recipe.servings} servings
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm opacity-70">No recipes yet. Start by adding your first recipe!</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
