:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#root {
  width: 100%;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
}

.input {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  margin-bottom: 15px;
}

.input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.grid {
  display: grid;
  gap: 20px;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.flex {
  display: flex;
  align-items: center;
  gap: 10px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-center {
  text-align: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: bold;
}

.opacity-70 {
  opacity: 0.7;
}

/* App Layout */
.app {
  display: flex;
  min-height: 100vh;
}

.navigation {
  width: 250px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  padding: 20px 0;
}

.nav-header {
  padding: 0 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 20px;
}

.nav-title {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
}

.nav-list {
  list-style: none;
  padding: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background: rgba(102, 126, 234, 0.2);
  color: white;
  border-left-color: #667eea;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* Dashboard Styles */
.dashboard-header {
  margin-bottom: 30px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.alert {
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.alert-danger {
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.alert-warning {
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.recipe-item {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.recipe-item:last-child {
  border-bottom: none;
}

/* Page Header */
.page-header {
  margin-bottom: 30px;
}

/* Search */
.search-bar {
  position: relative;
}

.search-input-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.7);
}

.search-input {
  padding-left: 45px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 30px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Inventory Items */
.inventory-item {
  transition: all 0.3s ease;
}

.inventory-item.expired {
  border-color: rgba(255, 107, 107, 0.5);
  background: rgba(255, 107, 107, 0.1);
}

.inventory-item.expiring-soon {
  border-color: rgba(255, 193, 7, 0.5);
  background: rgba(255, 193, 7, 0.1);
}

.btn-icon {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon.btn-danger {
  background: rgba(255, 107, 107, 0.3);
}

.btn-icon.btn-danger:hover {
  background: rgba(255, 107, 107, 0.5);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

/* Textarea */
textarea.input {
  resize: vertical;
  min-height: 80px;
}

/* Text Colors */
.text-red-400 {
  color: #f87171;
}

.text-yellow-400 {
  color: #facc15;
}

/* Recipe Styles */
.recipe-card {
  transition: all 0.3s ease;
}

.recipe-card:hover {
  transform: translateY(-2px);
}

.recipe-meta {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 10px;
}

.category-tag {
  background: rgba(102, 126, 234, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tags {
  display: flex;
  gap: 4px;
}

.tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
}

.recipe-detail {
  max-width: 800px;
}

.ingredient-list {
  list-style: none;
  padding: 0;
}

.ingredient-list li {
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ingredient-list li:last-child {
  border-bottom: none;
}

.instruction-list {
  list-style: none;
  padding: 0;
  counter-reset: step-counter;
}

.instruction-list li {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  padding-left: 40px;
}

.instruction-list li:last-child {
  border-bottom: none;
}

.step-number {
  position: absolute;
  left: 0;
  top: 10px;
  background: #667eea;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.form-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Meal Planning Styles */
.weekly-calendar {
  margin-bottom: 30px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 15px;
}

.calendar-day {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
}

.day-header {
  text-align: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.meal-slot {
  margin-bottom: 15px;
}

.meal-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.meal-type {
  font-size: 0.8rem;
  font-weight: bold;
  color: #667eea;
  text-transform: uppercase;
}

.add-meal-btn {
  background: rgba(102, 126, 234, 0.3);
  border: none;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
}

.add-meal-btn:hover {
  background: rgba(102, 126, 234, 0.5);
}

.meal-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  margin-bottom: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.meal-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.meal-name {
  font-size: 0.8rem;
  font-weight: 500;
}

.remove-meal-btn {
  background: rgba(255, 107, 107, 0.3);
  border: none;
  border-radius: 3px;
  padding: 2px;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
}

.remove-meal-btn:hover {
  background: rgba(255, 107, 107, 0.5);
}

.empty-meal-slot {
  padding: 8px;
  text-align: center;
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 6px;
}

.recipe-selection {
  max-height: 400px;
  overflow-y: auto;
}

.recipe-option {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.recipe-option:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.weekly-summary .summary-stats,
.weekly-summary .popular-recipes {
  space-y: 8px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
  border-bottom: none;
}

/* Shopping List Styles */
.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-controls {
  flex-shrink: 0;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.category-section {
  margin-bottom: 30px;
}

.category-header {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(102, 126, 234, 0.5);
  color: #667eea;
}

.items-grid {
  display: grid;
  gap: 10px;
}

.shopping-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.shopping-item.completed {
  opacity: 0.6;
  background: rgba(255, 255, 255, 0.05);
}

.item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-main {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox:hover {
  border-color: #667eea;
}

.checkbox.checked {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.item-details {
  flex: 1;
}

.item-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.item-name.completed-text {
  text-decoration: line-through;
  opacity: 0.7;
}

.quantity {
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
}

.item-notes {
  font-size: 0.9rem;
  opacity: 0.7;
  margin-bottom: 4px;
}

.item-meta {
  font-size: 0.8rem;
  opacity: 0.5;
}

@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }

  .navigation {
    width: 100%;
    padding: 10px 0;
  }

  .nav-list {
    display: flex;
    overflow-x: auto;
    padding: 0 10px;
  }

  .nav-link {
    flex-direction: column;
    gap: 5px;
    padding: 10px 15px;
    min-width: 80px;
    text-align: center;
    border-left: none;
    border-bottom: 3px solid transparent;
  }

  .nav-link.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .container {
    padding: 0 10px;
  }

  .card {
    margin: 5px;
    padding: 15px;
  }

  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }

  /* Mobile-specific adjustments */
  .page-header .flex-between {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .controls {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .filter-controls .flex {
    justify-content: center;
  }

  .calendar-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .calendar-day {
    min-height: auto;
  }

  .modal {
    width: 95%;
    padding: 20px;
    max-height: 95vh;
  }

  .recipe-selection {
    max-height: 300px;
  }

  .weekly-summary .grid-2 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 10px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .text-2xl {
    font-size: 1.25rem;
  }

  .text-xl {
    font-size: 1.1rem;
  }

  .nav-link {
    min-width: 70px;
    padding: 8px 12px;
  }

  .stat-card .flex-between {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .inventory-item .flex-between,
  .recipe-card .flex-between {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .inventory-item .flex,
  .recipe-card .flex {
    align-self: flex-end;
  }
}
