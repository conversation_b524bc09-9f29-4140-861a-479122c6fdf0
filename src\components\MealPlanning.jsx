import React, { useState, useContext } from 'react';
import { FoodContext } from '../App';
import { 
  Calendar, 
  Plus, 
  ChefHat, 
  Trash2, 
  ShoppingCart,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';
import { format, startOfWeek, addDays, addWeeks, subWeeks, isSameDay } from 'date-fns';

function MealPlanning() {
  const { recipes, mealPlan, setMealPlan, shoppingList, setShoppingList } = useContext(FoodContext);
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [showAddMeal, setShowAddMeal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedMealType, setSelectedMealType] = useState('');

  const mealTypes = ['Breakfast', 'Lunch', 'Dinner', 'Snack'];
  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 0 }); // Sunday
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  const getMealsForDate = (date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return mealPlan[dateKey] || [];
  };

  const addMealToPlan = (date, mealType, recipe) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const currentMeals = mealPlan[dateKey] || [];
    
    const newMeal = {
      id: Date.now().toString(),
      type: mealType,
      recipe: recipe,
      date: dateKey
    };

    setMealPlan({
      ...mealPlan,
      [dateKey]: [...currentMeals, newMeal]
    });

    setShowAddMeal(false);
    setSelectedDate(null);
    setSelectedMealType('');
  };

  const removeMealFromPlan = (date, mealId) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const currentMeals = mealPlan[dateKey] || [];
    
    setMealPlan({
      ...mealPlan,
      [dateKey]: currentMeals.filter(meal => meal.id !== mealId)
    });
  };

  const generateShoppingList = () => {
    const ingredients = new Map();
    
    // Get all meals for the current week
    weekDays.forEach(date => {
      const meals = getMealsForDate(date);
      meals.forEach(meal => {
        if (meal.recipe && meal.recipe.ingredients) {
          meal.recipe.ingredients.forEach(ingredient => {
            if (ingredients.has(ingredient)) {
              ingredients.set(ingredient, ingredients.get(ingredient) + 1);
            } else {
              ingredients.set(ingredient, 1);
            }
          });
        }
      });
    });

    // Convert to shopping list format
    const newShoppingItems = Array.from(ingredients.entries()).map(([ingredient, count]) => ({
      id: Date.now().toString() + Math.random(),
      name: count > 1 ? `${ingredient} (x${count})` : ingredient,
      completed: false,
      category: 'Generated from meal plan',
      dateAdded: new Date().toISOString()
    }));

    // Add to existing shopping list
    setShoppingList([...shoppingList, ...newShoppingItems]);
    
    alert(`Added ${newShoppingItems.length} items to your shopping list!`);
  };

  const navigateWeek = (direction) => {
    if (direction === 'prev') {
      setCurrentWeek(subWeeks(currentWeek, 1));
    } else {
      setCurrentWeek(addWeeks(currentWeek, 1));
    }
  };

  return (
    <div className="container">
      <div className="meal-planning">
        <header className="page-header">
          <div className="flex-between">
            <div>
              <h1 className="text-2xl font-bold mb-4">Meal Planning</h1>
              <p className="opacity-70">Plan your weekly meals and generate shopping lists</p>
            </div>
            <div className="flex gap-2">
              <button 
                className="btn btn-secondary"
                onClick={generateShoppingList}
                disabled={weekDays.every(date => getMealsForDate(date).length === 0)}
              >
                <ShoppingCart size={20} />
                Generate Shopping List
              </button>
            </div>
          </div>
        </header>

        {/* Week Navigation */}
        <div className="week-navigation mb-4">
          <div className="flex-between">
            <button className="btn btn-secondary" onClick={() => navigateWeek('prev')}>
              <ArrowLeft size={20} />
              Previous Week
            </button>
            <h2 className="text-xl font-bold">
              Week of {format(weekStart, 'MMM dd, yyyy')}
            </h2>
            <button className="btn btn-secondary" onClick={() => navigateWeek('next')}>
              Next Week
              <ArrowRight size={20} />
            </button>
          </div>
        </div>

        {/* Weekly Calendar */}
        <div className="weekly-calendar">
          <div className="calendar-grid">
            {weekDays.map(date => (
              <div key={date.toISOString()} className="calendar-day">
                <div className="day-header">
                  <h3 className="font-bold">{format(date, 'EEE')}</h3>
                  <p className="text-sm opacity-70">{format(date, 'MMM dd')}</p>
                </div>
                
                <div className="day-meals">
                  {mealTypes.map(mealType => {
                    const mealsOfType = getMealsForDate(date).filter(meal => meal.type === mealType);
                    
                    return (
                      <div key={mealType} className="meal-slot">
                        <div className="meal-type-header">
                          <span className="meal-type">{mealType}</span>
                          <button 
                            className="add-meal-btn"
                            onClick={() => {
                              setSelectedDate(date);
                              setSelectedMealType(mealType);
                              setShowAddMeal(true);
                            }}
                          >
                            <Plus size={14} />
                          </button>
                        </div>
                        
                        <div className="meals">
                          {mealsOfType.map(meal => (
                            <div key={meal.id} className="meal-item">
                              <div className="meal-content">
                                <span className="meal-name">{meal.recipe.name}</span>
                                <button 
                                  className="remove-meal-btn"
                                  onClick={() => removeMealFromPlan(date, meal.id)}
                                >
                                  <Trash2 size={12} />
                                </button>
                              </div>
                              <div className="meal-details">
                                <span className="text-xs opacity-70">
                                  {meal.recipe.cookTime}min • {meal.recipe.servings} servings
                                </span>
                              </div>
                            </div>
                          ))}
                          
                          {mealsOfType.length === 0 && (
                            <div className="empty-meal-slot">
                              <span className="text-xs opacity-50">No meal planned</span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Add Meal Modal */}
        {showAddMeal && selectedDate && (
          <div className="modal-overlay">
            <div className="modal">
              <h2 className="text-xl font-bold mb-4">
                Add {selectedMealType} for {format(selectedDate, 'EEEE, MMM dd')}
              </h2>
              
              {recipes.length === 0 ? (
                <div className="empty-state">
                  <ChefHat size={48} className="opacity-50 mb-4" />
                  <p className="opacity-70 mb-4">No recipes available. Add some recipes first!</p>
                  <button 
                    className="btn btn-secondary"
                    onClick={() => setShowAddMeal(false)}
                  >
                    Close
                  </button>
                </div>
              ) : (
                <div>
                  <div className="recipe-selection">
                    {recipes.map(recipe => (
                      <div 
                        key={recipe.id} 
                        className="recipe-option"
                        onClick={() => addMealToPlan(selectedDate, selectedMealType, recipe)}
                      >
                        <div className="recipe-info">
                          <h3 className="font-bold">{recipe.name}</h3>
                          <p className="text-sm opacity-70">
                            {recipe.cookTime}min • {recipe.servings} servings • {recipe.category}
                          </p>
                          {recipe.description && (
                            <p className="text-xs opacity-60 mt-1">{recipe.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    <button 
                      className="btn btn-secondary"
                      onClick={() => {
                        setShowAddMeal(false);
                        setSelectedDate(null);
                        setSelectedMealType('');
                      }}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Weekly Summary */}
        <div className="weekly-summary mt-6">
          <div className="card">
            <h2 className="text-xl font-bold mb-4">Weekly Summary</h2>
            <div className="grid grid-2">
              <div>
                <h3 className="font-bold mb-2">Planned Meals</h3>
                <div className="summary-stats">
                  {mealTypes.map(type => {
                    const count = weekDays.reduce((total, date) => {
                      return total + getMealsForDate(date).filter(meal => meal.type === type).length;
                    }, 0);
                    
                    return (
                      <div key={type} className="stat-row">
                        <span>{type}:</span>
                        <span className="font-bold">{count}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
              
              <div>
                <h3 className="font-bold mb-2">Most Used Recipes</h3>
                <div className="popular-recipes">
                  {(() => {
                    const recipeCount = new Map();
                    weekDays.forEach(date => {
                      getMealsForDate(date).forEach(meal => {
                        if (meal.recipe) {
                          const count = recipeCount.get(meal.recipe.name) || 0;
                          recipeCount.set(meal.recipe.name, count + 1);
                        }
                      });
                    });
                    
                    const sortedRecipes = Array.from(recipeCount.entries())
                      .sort((a, b) => b[1] - a[1])
                      .slice(0, 3);
                    
                    return sortedRecipes.length > 0 ? (
                      sortedRecipes.map(([name, count]) => (
                        <div key={name} className="stat-row">
                          <span>{name}:</span>
                          <span className="font-bold">{count}x</span>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm opacity-70">No recipes planned yet</p>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MealPlanning;
