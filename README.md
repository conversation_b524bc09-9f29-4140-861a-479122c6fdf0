# Food Management System

A comprehensive web application for managing your food inventory, recipes, meal planning, and shopping lists. Built with React and modern web technologies.

## Features

### 🏠 Dashboard
- Overview of your food inventory
- Expiration alerts for items expiring soon or already expired
- Recent recipes display
- Quick statistics on inventory, recipes, and meal plans

### 📦 Food Inventory Management
- Add, edit, and delete food items
- Track quantities, expiration dates, and storage locations
- Categorize items (Fruits, Vegetables, Meat, Dairy, etc.)
- Visual alerts for expired and expiring items
- Search and filter functionality

### 👨‍🍳 Recipe Collection
- Store and organize your favorite recipes
- Add ingredients, instructions, cooking times, and difficulty levels
- Categorize recipes by meal type
- Tag recipes for easy searching
- Detailed recipe view with step-by-step instructions

### 📅 Meal Planning
- Weekly meal planning calendar
- Drag-and-drop interface for planning meals
- Generate shopping lists from meal plans
- Track planned meals by type (Breakfast, Lunch, Dinner, Snacks)
- Weekly summary with meal statistics

### 🛒 Shopping List
- Add items manually or generate from meal plans
- Organize items by category
- Mark items as completed while shopping
- Filter by completed/pending items
- Clear completed items in bulk

## Technology Stack

- **Frontend**: React 18 with Hooks
- **Routing**: React Router DOM
- **Styling**: Custom CSS with modern design
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Build Tool**: Vite
- **Data Persistence**: Local Storage

## Installation

1. Make sure you have Node.js installed on your system
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   npm install
   ```
4. Start the development server:
   ```bash
   npm run dev
   ```
5. Open your browser and navigate to `http://localhost:5173`

## Usage

### Getting Started
1. **Add Food Items**: Start by adding items to your inventory with expiration dates
2. **Create Recipes**: Build your recipe collection with ingredients and instructions
3. **Plan Meals**: Use the meal planning calendar to plan your weekly meals
4. **Generate Shopping Lists**: Create shopping lists from your meal plans or add items manually

### Data Persistence
All your data is automatically saved to your browser's local storage, so your information persists between sessions.

## Project Structure

```
src/
├── components/
│   ├── Dashboard.jsx      # Main dashboard with overview
│   ├── Inventory.jsx      # Food inventory management
│   ├── Recipes.jsx        # Recipe collection and management
│   ├── MealPlanning.jsx   # Weekly meal planning
│   └── ShoppingList.jsx   # Shopping list management
├── App.jsx                # Main app component with routing
├── main.jsx              # App entry point
└── index.css             # Global styles
```

## Features in Detail

### Inventory Management
- **Smart Categorization**: Organize items by food categories
- **Expiration Tracking**: Visual alerts for items expiring within 3 days
- **Storage Locations**: Track where items are stored (fridge, pantry, etc.)
- **Quantity Tracking**: Monitor quantities with various units

### Recipe Management
- **Comprehensive Details**: Store prep time, cook time, servings, and difficulty
- **Ingredient Lists**: Detailed ingredient lists with quantities
- **Step-by-Step Instructions**: Clear cooking instructions
- **Tagging System**: Tag recipes for easy categorization and searching

### Meal Planning
- **Weekly View**: Plan meals for the entire week
- **Multiple Meal Types**: Support for breakfast, lunch, dinner, and snacks
- **Recipe Integration**: Select from your saved recipes for meal planning
- **Shopping List Generation**: Automatically create shopping lists from planned meals

### Shopping List
- **Category Organization**: Items grouped by shopping categories
- **Progress Tracking**: Mark items as completed while shopping
- **Flexible Adding**: Add items manually or generate from meal plans
- **Smart Filtering**: Filter by completion status

## Responsive Design

The application is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Contributing

This is a personal food management system. Feel free to fork and customize it for your own needs!

## License

This project is open source and available under the MIT License.
