import React, { useState, useContext } from 'react';
import { FoodContext } from '../App';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Package,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { format, parseISO, isAfter } from 'date-fns';

function Inventory() {
  const { inventory, setInventory } = useContext(FoodContext);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    quantity: '',
    unit: '',
    expirationDate: '',
    location: '',
    notes: ''
  });

  const categories = [
    'Fruits', 'Vegetables', 'Meat', 'Dairy', 'Grains', 
    'Pantry', 'Frozen', 'Beverages', 'Snacks', 'Other'
  ];

  const units = [
    'pieces', 'lbs', 'oz', 'kg', 'g', 'cups', 'tbsp', 'tsp', 
    'liters', 'ml', 'cans', 'bottles', 'packages'
  ];

  // Filter inventory based on search term
  const filteredInventory = inventory.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingItem) {
      // Update existing item
      setInventory(inventory.map(item => 
        item.id === editingItem.id 
          ? { ...formData, id: editingItem.id }
          : item
      ));
      setEditingItem(null);
    } else {
      // Add new item
      const newItem = {
        ...formData,
        id: Date.now().toString(),
        dateAdded: new Date().toISOString()
      };
      setInventory([...inventory, newItem]);
    }
    
    // Reset form
    setFormData({
      name: '',
      category: '',
      quantity: '',
      unit: '',
      expirationDate: '',
      location: '',
      notes: ''
    });
    setShowAddForm(false);
  };

  const handleEdit = (item) => {
    setFormData(item);
    setEditingItem(item);
    setShowAddForm(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      setInventory(inventory.filter(item => item.id !== id));
    }
  };

  const isExpired = (expirationDate) => {
    if (!expirationDate) return false;
    return isAfter(new Date(), parseISO(expirationDate));
  };

  const isExpiringSoon = (expirationDate) => {
    if (!expirationDate) return false;
    const expDate = parseISO(expirationDate);
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    return isAfter(threeDaysFromNow, expDate) && isAfter(expDate, new Date());
  };

  return (
    <div className="container">
      <div className="inventory">
        <header className="page-header">
          <div className="flex-between">
            <div>
              <h1 className="text-2xl font-bold mb-4">Food Inventory</h1>
              <p className="opacity-70">Manage your food items and track expiration dates</p>
            </div>
            <button 
              className="btn"
              onClick={() => setShowAddForm(true)}
            >
              <Plus size={20} />
              Add Item
            </button>
          </div>
        </header>

        {/* Search Bar */}
        <div className="search-bar mb-4">
          <div className="search-input-container">
            <Search size={20} className="search-icon" />
            <input
              type="text"
              placeholder="Search inventory..."
              className="input search-input"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Add/Edit Form */}
        {showAddForm && (
          <div className="modal-overlay">
            <div className="modal">
              <h2 className="text-xl font-bold mb-4">
                {editingItem ? 'Edit Item' : 'Add New Item'}
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="grid grid-2">
                  <input
                    type="text"
                    placeholder="Item name"
                    className="input"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    required
                  />
                  <select
                    className="input"
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    required
                  >
                    <option value="">Select category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                </div>
                
                <div className="grid grid-2">
                  <input
                    type="number"
                    placeholder="Quantity"
                    className="input"
                    value={formData.quantity}
                    onChange={(e) => setFormData({...formData, quantity: e.target.value})}
                    required
                  />
                  <select
                    className="input"
                    value={formData.unit}
                    onChange={(e) => setFormData({...formData, unit: e.target.value})}
                    required
                  >
                    <option value="">Select unit</option>
                    {units.map(unit => (
                      <option key={unit} value={unit}>{unit}</option>
                    ))}
                  </select>
                </div>

                <input
                  type="date"
                  placeholder="Expiration date"
                  className="input"
                  value={formData.expirationDate}
                  onChange={(e) => setFormData({...formData, expirationDate: e.target.value})}
                />

                <input
                  type="text"
                  placeholder="Storage location (e.g., Fridge, Pantry)"
                  className="input"
                  value={formData.location}
                  onChange={(e) => setFormData({...formData, location: e.target.value})}
                />

                <textarea
                  placeholder="Notes (optional)"
                  className="input"
                  rows="3"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />

                <div className="flex gap-2">
                  <button type="submit" className="btn">
                    {editingItem ? 'Update Item' : 'Add Item'}
                  </button>
                  <button 
                    type="button" 
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingItem(null);
                      setFormData({
                        name: '', category: '', quantity: '', unit: '',
                        expirationDate: '', location: '', notes: ''
                      });
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Inventory Grid */}
        <div className="grid grid-3">
          {filteredInventory.map(item => (
            <div key={item.id} className={`card inventory-item ${
              isExpired(item.expirationDate) ? 'expired' : 
              isExpiringSoon(item.expirationDate) ? 'expiring-soon' : ''
            }`}>
              <div className="flex-between mb-2">
                <h3 className="font-bold">{item.name}</h3>
                <div className="flex gap-1">
                  <button 
                    className="btn-icon"
                    onClick={() => handleEdit(item)}
                  >
                    <Edit size={16} />
                  </button>
                  <button 
                    className="btn-icon btn-danger"
                    onClick={() => handleDelete(item.id)}
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
              
              <div className="item-details">
                <p className="text-sm opacity-70 mb-1">
                  <Package size={14} className="inline mr-1" />
                  {item.quantity} {item.unit} • {item.category}
                </p>
                
                {item.expirationDate && (
                  <p className={`text-sm mb-1 flex items-center ${
                    isExpired(item.expirationDate) ? 'text-red-400' :
                    isExpiringSoon(item.expirationDate) ? 'text-yellow-400' : 'opacity-70'
                  }`}>
                    {(isExpired(item.expirationDate) || isExpiringSoon(item.expirationDate)) && (
                      <AlertTriangle size={14} className="mr-1" />
                    )}
                    <Calendar size={14} className="mr-1" />
                    Expires: {format(parseISO(item.expirationDate), 'MMM dd, yyyy')}
                  </p>
                )}
                
                {item.location && (
                  <p className="text-sm opacity-70 mb-1">📍 {item.location}</p>
                )}
                
                {item.notes && (
                  <p className="text-sm opacity-70">💭 {item.notes}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {filteredInventory.length === 0 && (
          <div className="empty-state">
            <Package size={48} className="opacity-50 mb-4" />
            <h3 className="text-lg font-bold mb-2">No items found</h3>
            <p className="opacity-70 mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Start by adding your first food item'}
            </p>
            {!searchTerm && (
              <button className="btn" onClick={() => setShowAddForm(true)}>
                <Plus size={20} />
                Add Your First Item
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default Inventory;
