import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Package, 
  ChefHat, 
  Calendar, 
  ShoppingCart, 
  Settings,
  Plus,
  Search
} from 'lucide-react';

// Import components (we'll create these next)
import Dashboard from './components/Dashboard';
import Inventory from './components/Inventory';
import Recipes from './components/Recipes';
import MealPlanning from './components/MealPlanning';
import ShoppingList from './components/ShoppingList';

// Context for global state management
export const FoodContext = React.createContext();

function App() {
  // Global state
  const [inventory, setInventory] = useState([]);
  const [recipes, setRecipes] = useState([]);
  const [mealPlan, setMealPlan] = useState({});
  const [shoppingList, setShoppingList] = useState([]);

  // Load data from localStorage on app start
  useEffect(() => {
    const savedInventory = localStorage.getItem('food-inventory');
    const savedRecipes = localStorage.getItem('food-recipes');
    const savedMealPlan = localStorage.getItem('food-meal-plan');
    const savedShoppingList = localStorage.getItem('food-shopping-list');

    if (savedInventory) setInventory(JSON.parse(savedInventory));
    if (savedRecipes) setRecipes(JSON.parse(savedRecipes));
    if (savedMealPlan) setMealPlan(JSON.parse(savedMealPlan));
    if (savedShoppingList) setShoppingList(JSON.parse(savedShoppingList));
  }, []);

  // Save data to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem('food-inventory', JSON.stringify(inventory));
  }, [inventory]);

  useEffect(() => {
    localStorage.setItem('food-recipes', JSON.stringify(recipes));
  }, [recipes]);

  useEffect(() => {
    localStorage.setItem('food-meal-plan', JSON.stringify(mealPlan));
  }, [mealPlan]);

  useEffect(() => {
    localStorage.setItem('food-shopping-list', JSON.stringify(shoppingList));
  }, [shoppingList]);

  const contextValue = {
    inventory,
    setInventory,
    recipes,
    setRecipes,
    mealPlan,
    setMealPlan,
    shoppingList,
    setShoppingList
  };

  return (
    <FoodContext.Provider value={contextValue}>
      <Router>
        <div className="app">
          <Navigation />
          <main className="main-content">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/inventory" element={<Inventory />} />
              <Route path="/recipes" element={<Recipes />} />
              <Route path="/meal-planning" element={<MealPlanning />} />
              <Route path="/shopping-list" element={<ShoppingList />} />
            </Routes>
          </main>
        </div>
      </Router>
    </FoodContext.Provider>
  );
}

function Navigation() {
  const location = useLocation();
  
  const navItems = [
    { path: '/', icon: Home, label: 'Dashboard' },
    { path: '/inventory', icon: Package, label: 'Inventory' },
    { path: '/recipes', icon: ChefHat, label: 'Recipes' },
    { path: '/meal-planning', icon: Calendar, label: 'Meal Planning' },
    { path: '/shopping-list', icon: ShoppingCart, label: 'Shopping List' },
  ];

  return (
    <nav className="navigation">
      <div className="nav-header">
        <h1 className="nav-title">🍽️ Food Manager</h1>
      </div>
      <ul className="nav-list">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <li key={item.path}>
              <Link 
                to={item.path} 
                className={`nav-link ${isActive ? 'active' : ''}`}
              >
                <Icon size={20} />
                <span>{item.label}</span>
              </Link>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}

export default App;
