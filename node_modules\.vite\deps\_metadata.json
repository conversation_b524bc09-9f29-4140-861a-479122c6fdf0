{"hash": "da237376", "browserHash": "9707bdc1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "79556116", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "9f92a7fb", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "8e92061c", "needsInterop": true}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "2215de28", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "bcb7bf31", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "3098ee95", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "7e2ceca4", "needsInterop": false}}, "chunks": {"chunk-WALXKXZM": {"file": "chunk-WALXKXZM.js"}, "chunk-WQMOH32Y": {"file": "chunk-WQMOH32Y.js"}, "chunk-5WWUZCGV": {"file": "chunk-5WWUZCGV.js"}}}